#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 一键运行各种实验
支持多种预设实验配置，便于快速测试和验证
"""

import argparse
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def run_quick_test():
    """快速测试 - 验证系统是否正常工作"""
    print("🧪 运行快速测试...")
    
    from src.experiment_controller import ExperimentController
    from src.experiment_manager import ExperimentConfig
    
    # 创建简单的测试实验
    test_config = ExperimentConfig(
        model_name='LeNet',
        dataset='MNIST',
        aggregator='FedAvg',
        n_participant=3,
        global_epoch=3,
        local_epoch=1,
        enable_pol=False,
        seed=42
    )
    
    controller = ExperimentController(
        output_dir="quick_test_results",
        max_workers=1,
        verbose=True
    )
    
    results = controller.run_custom_experiments([test_config])
    
    if results and results[0].success:
        print("✅ 快速测试通过！系统运行正常")
        return True
    else:
        print("❌ 快速测试失败！请检查环境配置")
        return False

def run_pol_demo():
    """PoL功能演示"""
    print("🎯 运行PoL功能演示...")
    
    from src.experiment_controller import ExperimentController
    from src.experiment_manager import ExperimentConfig
    
    # 创建对比实验：FedAvg vs martFL vs martFL+PoL
    experiments = [
        # FedAvg基线
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='FedAvg',
            n_participant=5,
            global_epoch=10,
            enable_pol=False,
            seed=42
        ),
        # martFL
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='martFL',
            n_participant=5,
            global_epoch=10,
            enable_pol=False,
            seed=42
        ),
        # martFL + PoL
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='martFL',
            n_participant=5,
            global_epoch=10,
            enable_pol=True,
            pol_entropy_analysis=True,
            pol_quality_aware=True,
            seed=42
        )
    ]
    
    controller = ExperimentController(
        output_dir="pol_demo_results",
        max_workers=2,
        verbose=True
    )
    
    results = controller.run_custom_experiments(experiments)
    controller.generate_final_analysis()
    
    print("✅ PoL演示完成！请查看 pol_demo_results 目录")

def run_attack_demo():
    """攻击防御演示"""
    print("⚔️ 运行攻击防御演示...")
    
    from src.experiment_controller import ExperimentController
    from src.experiment_manager import ExperimentConfig
    
    # 创建攻击场景实验
    experiments = [
        # 无攻击基线
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='martFL',
            n_participant=8,
            n_adversary=0,
            attack='None',
            global_epoch=15,
            enable_pol=True,
            seed=42
        ),
        # 标签翻转攻击
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='martFL',
            n_participant=8,
            n_adversary=2,
            attack='label_flipping',
            global_epoch=15,
            enable_pol=True,
            seed=42
        ),
        # 后门攻击
        ExperimentConfig(
            model_name='LeNet',
            dataset='MNIST',
            aggregator='martFL',
            n_participant=8,
            n_adversary=2,
            attack='backdoor',
            global_epoch=15,
            enable_pol=True,
            seed=42
        )
    ]
    
    controller = ExperimentController(
        output_dir="attack_demo_results",
        max_workers=2,
        verbose=True
    )
    
    results = controller.run_custom_experiments(experiments)
    controller.generate_final_analysis()
    
    print("✅ 攻击防御演示完成！请查看 attack_demo_results 目录")

def run_comprehensive_benchmark():
    """运行综合基准测试"""
    print("🏆 运行综合基准测试...")
    
    from src.experiment_controller import ExperimentController
    
    controller = ExperimentController(
        output_dir="comprehensive_benchmark_results",
        max_workers=None,  # 自动检测
        verbose=True
    )
    
    results = controller.run_comprehensive_experiments()
    
    print("✅ 综合基准测试完成！请查看 comprehensive_benchmark_results 目录")

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")

    try:
        # 先直接测试PyTorch
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        print(f"GPU数量: {torch.cuda.device_count()}")

        from src.device_manager import DeviceManager

        dm = DeviceManager(verbose=True)

        print("\n📋 环境检查结果:")
        print(f"✅ Python环境: 正常")
        print(f"✅ PyTorch: 正常")
        print(f"✅ 设备管理: 正常")

        if dm.gpu_info:
            print(f"✅ GPU: {len(dm.gpu_info)} 个可用")
            for gpu in dm.gpu_info:
                print(f"   - GPU {gpu['id']}: {gpu['name']}")
        else:
            print(f"⚠️ GPU: 未检测到，将使用CPU")

        print(f"✅ CPU: {dm.system_info.get('cpu_physical_cores', 0)} 核心")
        print(f"✅ 内存: {dm.system_info.get('memory_total_gb', 0):.1f} GB")

        return True

    except Exception as e:
        print(f"❌ 环境检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='martFL-PoL 快速启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
可用命令:
  check       - 检查运行环境
  test        - 快速测试（验证系统是否正常）
  pol-demo    - PoL功能演示（对比FedAvg vs martFL vs martFL+PoL）
  attack-demo - 攻击防御演示（展示PoL的防御能力）
  benchmark   - 综合基准测试（完整的实验套件）

示例用法:
  python run_experiments.py check
  python run_experiments.py test
  python run_experiments.py pol-demo
  python run_experiments.py attack-demo
  python run_experiments.py benchmark
        """
    )
    
    parser.add_argument('command', 
                       choices=['check', 'test', 'pol-demo', 'attack-demo', 'benchmark'],
                       help='要执行的命令')
    
    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    print("🚀 martFL-PoL 实验框架")
    print("=" * 50)
    
    try:
        if args.command == 'check':
            success = check_environment()
            sys.exit(0 if success else 1)
            
        elif args.command == 'test':
            success = run_quick_test()
            sys.exit(0 if success else 1)
            
        elif args.command == 'pol-demo':
            run_pol_demo()
            
        elif args.command == 'attack-demo':
            run_attack_demo()
            
        elif args.command == 'benchmark':
            run_comprehensive_benchmark()
        
        print("\n🎉 任务完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 任务被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 任务执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

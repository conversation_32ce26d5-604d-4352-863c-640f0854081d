#!/usr/bin/env python3
"""
martFL + PoL 综合诊断工具
深入分析原始martFL和PoL的工作机制，诊断集成问题
"""

import os
import sys
import json
import torch
import numpy as np
from pol_integration import ProofVerifier
from pol_utils import parameter_distance

def analyze_proof_structure(proof_dir):
    """分析证明结构"""
    print(f"\n📁 分析证明结构: {os.path.basename(proof_dir)}")
    
    # 检查文件
    files = os.listdir(proof_dir) if os.path.exists(proof_dir) else []
    print(f"   文件列表: {files}")
    
    # 分析元数据
    metadata_path = os.path.join(proof_dir, "metadata.json")
    if os.path.exists(metadata_path):
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        print(f"   参与者ID: {metadata.get('participant_id', 'N/A')}")
        print(f"   训练步数: {metadata.get('total_steps', 'N/A')}")
        print(f"   检查点数: {metadata.get('checkpoint_count', 'N/A')}")
        print(f"   数据样本数: {metadata.get('dataset_size', 'N/A')}")
    
    # 分析检查点
    checkpoints = [f for f in files if f.startswith('checkpoint_') and f.endswith('.pt')]
    checkpoints.sort()
    print(f"   检查点文件: {len(checkpoints)}个")
    for ckpt in checkpoints:
        print(f"     - {ckpt}")
    
    return checkpoints

def analyze_parameter_distances(proof_dir, threshold=10.0):
    """分析参数距离"""
    print(f"\n📊 分析参数距离 (阈值: {threshold})")
    
    checkpoints = [f for f in os.listdir(proof_dir) 
                  if f.startswith('checkpoint_') and f.endswith('.pt')]
    checkpoints.sort()
    
    if len(checkpoints) < 2:
        print(f"   ❌ 检查点不足: {len(checkpoints)}")
        return []
    
    distances = []
    for i in range(len(checkpoints) - 1):
        ckpt1_path = os.path.join(proof_dir, checkpoints[i])
        ckpt2_path = os.path.join(proof_dir, checkpoints[i + 1])
        
        try:
            # 直接加载检查点计算距离
            ckpt1 = torch.load(ckpt1_path, map_location='cpu')
            ckpt2 = torch.load(ckpt2_path, map_location='cpu')

            # 提取模型参数
            if 'model_state_dict' in ckpt1:
                params1 = ckpt1['model_state_dict']
                params2 = ckpt2['model_state_dict']
            elif 'net' in ckpt1:
                params1 = ckpt1['net']
                params2 = ckpt2['net']
            else:
                params1 = ckpt1
                params2 = ckpt2

            # 计算L2距离
            dist = 0.0
            for key in params1.keys():
                if key in params2:
                    diff = params1[key] - params2[key]
                    dist += torch.norm(diff).item() ** 2
            dist = dist ** 0.5

            distances.append(dist)
            
            status = "✅" if dist <= threshold else "❌"
            print(f"   {status} {checkpoints[i]} -> {checkpoints[i+1]}: {dist:.6f}")
            
        except Exception as e:
            print(f"   ❌ 计算失败 {checkpoints[i]} -> {checkpoints[i+1]}: {e}")
            distances.append(float('inf'))
    
    if distances:
        print(f"   📈 统计信息:")
        print(f"     平均距离: {np.mean(distances):.6f}")
        print(f"     最大距离: {np.max(distances):.6f}")
        print(f"     最小距离: {np.min(distances):.6f}")
        print(f"     超过阈值: {sum(1 for d in distances if d > threshold)}/{len(distances)}")
    
    return distances

def compare_with_original_pol_thresholds(distances):
    """与原始PoL论文阈值对比"""
    print(f"\n🔬 与原始PoL论文阈值对比")
    
    if not distances:
        print("   无距离数据")
        return
    
    # 原始PoL论文的推荐阈值
    original_thresholds = {
        'L1': 1000,
        'L2': 10,
        'L∞': 0.1,
        'cosine': 0.01
    }
    
    print(f"   原始论文L2阈值: {original_thresholds['L2']}")
    print(f"   我们的平均L2距离: {np.mean(distances):.6f}")
    print(f"   我们的最大L2距离: {np.max(distances):.6f}")
    
    # 建议的阈值
    suggested_threshold = max(np.max(distances) * 1.2, original_thresholds['L2'])
    print(f"   建议阈值: {suggested_threshold:.2f}")
    
    return suggested_threshold

def analyze_data_distribution_impact():
    """分析数据分布对PoL验证的影响"""
    print(f"\n📊 分析数据分布影响")
    
    # 从最近的运行日志中提取数据分布信息
    # 这里我们模拟分析
    distributions = {
        0: 1000,    # 参与者0：1000个样本
        1: 14750,   # 参与者1：14750个样本
        2: 14750,   # 参与者2：14750个样本
        3: 14750,   # 参与者3：14750个样本
        4: 14750,   # 参与者4：14750个样本
    }
    
    print("   数据分布:")
    for pid, count in distributions.items():
        print(f"     参与者{pid}: {count}个样本")
    
    # 分析不平衡程度
    total_samples = sum(distributions.values())
    min_samples = min(distributions.values())
    max_samples = max(distributions.values())
    
    print(f"   不平衡分析:")
    print(f"     总样本数: {total_samples}")
    print(f"     最小样本数: {min_samples}")
    print(f"     最大样本数: {max_samples}")
    print(f"     不平衡比例: {max_samples/min_samples:.1f}:1")
    
    if max_samples / min_samples > 10:
        print(f"   ⚠️ 严重数据不平衡！可能导致PoL验证失败")
        print(f"   💡 建议使用更均衡的数据分布")

def diagnose_martfl_pol_integration():
    """诊断martFL+PoL集成问题"""
    print(f"\n🔧 诊断martFL+PoL集成问题")
    
    print("   可能的问题:")
    print("   1. PoL验证阈值过于严格")
    print("   2. 数据分布不均导致训练轨迹差异巨大")
    print("   3. PoL验证失败影响martFL聚合权重计算")
    print("   4. 联邦学习环境与原始PoL设计场景不匹配")
    
    print("\n   建议的解决方案:")
    print("   1. 调整PoL验证阈值到更合理的值")
    print("   2. 使用更均衡的数据分布")
    print("   3. 修改聚合逻辑，让PoL验证失败不完全阻止训练")
    print("   4. 实现适合联邦学习的PoL验证策略")

def main():
    """主诊断流程"""
    print("🔍 martFL + PoL 综合诊断工具")
    print("=" * 50)
    
    # 1. 分析数据分布影响
    analyze_data_distribution_impact()
    
    # 2. 查找最近的证明目录
    proof_base_dir = "proof"
    if not os.path.exists(proof_base_dir):
        print(f"\n❌ 证明目录不存在: {proof_base_dir}")
        return
    
    # 找到最近的证明
    proof_dirs = []
    for item in os.listdir(proof_base_dir):
        item_path = os.path.join(proof_base_dir, item)
        if os.path.isdir(item_path) and item.startswith("participant_"):
            proof_dirs.append(item_path)
    
    proof_dirs.sort(reverse=True)
    recent_proofs = proof_dirs[:5]  # 最近的5个
    
    print(f"\n📋 分析最近的 {len(recent_proofs)} 个证明:")
    
    all_distances = []
    for proof_dir in recent_proofs:
        # 3. 分析证明结构
        checkpoints = analyze_proof_structure(proof_dir)
        
        # 4. 分析参数距离
        distances = analyze_parameter_distances(proof_dir)
        all_distances.extend(distances)
    
    # 5. 与原始PoL阈值对比
    if all_distances:
        suggested_threshold = compare_with_original_pol_thresholds(all_distances)
        
        print(f"\n💡 建议的命令:")
        print(f"   python main.py --enable_pol --pol_verification_threshold {suggested_threshold:.1f}")
    
    # 6. 集成问题诊断
    diagnose_martfl_pol_integration()
    
    print("\n" + "=" * 50)
    print("诊断完成！")

if __name__ == "__main__":
    main()

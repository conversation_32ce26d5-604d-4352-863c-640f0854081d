import argparse
import os
import torch

def Config():
    """
    martFL配置管理 - 优雅集成PoL功能
    保持原有martFL配置不变，仅添加PoL相关选项
    """
    parser = argparse.ArgumentParser(description='martFL with optional Proof-of-Learning integration')

    # === 原始martFL配置 ===
    parser.add_argument('-m','--model_name', type=str, default='LeNet', help='model name')
    parser.add_argument('-ds','--dataset', type=str, default='MNIST', help='dataset')
    parser.add_argument('-ag','--aggregator', type=str, default='FedAvg', help='aggregator')
    parser.add_argument('-np','--n_participant', type=int, default=5, help='the number of clients in model training')
    parser.add_argument('-a','--attack', type=str, default='None', help='attack method')
    parser.add_argument('-na','--n_adversary', type=int, default=0, help='the number of clients is malicious')
    parser.add_argument('-lr','--learning_rate', type=float, default=0.001, help='the learning rate in model training')
    parser.add_argument('-ld','--lr_decay',type=int,default=0,help='the number of epoches to decay learning rate')
    parser.add_argument('-o','--optimizer', type=str,default='SGD', help='optimizer in model training')
    parser.add_argument('-lf','--loss_fn', type=str,default='CE', help='loss function')
    parser.add_argument('-bz','--batch_size',type=int, default=32, help='the sample number in each batch')
    parser.add_argument('-ge','--global_epoch',type=int, default=5, help='rounds to aggregate')
    parser.add_argument('-le','--local_epoch', type=int, default=2, help='rounds to train locally')
    parser.add_argument('-ss','--sample_split', type=str, default='uni', help='the distribution of sample number')
    parser.add_argument('-cs','--class_split', type=str, default='uni', help='the distribution of class number')
    parser.add_argument('-dv','--device',type=str,default='auto',help='the device used to train model (auto/cuda:0/cpu)')
    parser.add_argument('-se','--semaphore',type=int,default=5,help='semaphore number')
    parser.add_argument('-al','--alpha',type=int,default=0,help='alpha')
    parser.add_argument('-rd','--root_dataset',type=int,default=1000,help='size of root dataset')
    parser.add_argument('-cb','--change_base',type=bool,default=False,help='change base')
    parser.add_argument('-sr','--server_ratio',type=float,default=0.5,help='server train-test ratio')
    parser.add_argument('-q','--quantization',type=bool,default=False,help='quantized aggregation')
    parser.add_argument('-ft','--fine_tuning',type=bool,default=False,help='fine-tuning')
    parser.add_argument('-fte','--fine_tuning_epoch',type=int,default=100,help='fine-tuning epoch')

    # === PoL集成配置（简化版本） ===
    parser.add_argument('--enable_pol', action='store_true', default=False,
                       help='启用Proof-of-Learning功能')
    parser.add_argument('--pol_proof_dir', type=str, default='proof',
                       help='PoL证明存储目录')

    # 基础PoL参数 - 基于IEEE S&P 2021论文的建议
    parser.add_argument('--pol_verification_threshold', type=float, default=10.0,
                       help='L2距离验证阈值（默认10.0，基于PoL论文建议，适合大多数模型）')
    parser.add_argument('--pol_verification_budget', type=int, default=2,
                       help='每轮验证的更新数量（论文建议2）')

    # 高级功能（可选）
    parser.add_argument('--pol_entropy_analysis', action='store_true', default=False,
                       help='启用熵增长安全性分析')
    parser.add_argument('--pol_quality_aware', action='store_true', default=False,
                       help='启用质量感知聚合（注意：原martFL已有聚合算法）')

    # 存储优化
    parser.add_argument('--pol_storage_precision', type=str, default='float32',
                       choices=['float16', 'float32'],
                       help='存储精度（float16节省空间但可能有兼容性问题）')

    args = parser.parse_args()

    # 智能设备检测和环境变量支持
    args.enable_pol = args.enable_pol or os.environ.get('ENABLE_POL', 'false').lower() == 'true'

    return args

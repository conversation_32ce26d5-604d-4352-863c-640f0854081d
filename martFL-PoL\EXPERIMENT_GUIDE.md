# martFL-PoL 实验框架使用指南

## 🎯 概述

这是一个完整的 martFL + Proof-of-Learning 实验框架，支持：
- 🔄 **并行执行**: 多GPU/CPU并行运行实验
- 📊 **自动分析**: 自动生成可视化报告和统计分析
- 🎛️ **智能设备管理**: 自动检测和分配GPU/CPU资源
- 📝 **详细记录**: 完整的实验过程和结果记录
- 🚀 **一键运行**: 预设多种实验场景

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查运行环境
python run_experiments.py check
```

### 2. 快速测试
```bash
# 运行快速测试（3分钟内完成）
python run_experiments.py test
```

### 3. PoL功能演示
```bash
# 对比 FedAvg vs martFL vs martFL+PoL
python run_experiments.py pol-demo
```

### 4. 攻击防御演示
```bash
# 展示PoL的攻击防御能力
python run_experiments.py attack-demo
```

### 5. 综合基准测试
```bash
# 运行完整的实验套件（可能需要数小时）
python run_experiments.py benchmark
```

## 🔧 高级使用

### 使用实验控制器

```bash
# 基线对比实验
python src/experiment_controller.py baseline --max_workers 4

# 攻击鲁棒性实验
python src/experiment_controller.py attack --max_workers 2

# 扩展性实验
python src/experiment_controller.py scalability

# 综合实验
python src/experiment_controller.py comprehensive --output_dir my_results
```

### 自定义实验配置

1. 创建配置文件 `my_experiments.json`:
```json
[
  {
    "model_name": "LeNet",
    "dataset": "MNIST",
    "aggregator": "martFL",
    "n_participant": 10,
    "n_adversary": 2,
    "attack": "label_flipping",
    "global_epoch": 20,
    "enable_pol": true,
    "pol_entropy_analysis": true,
    "seed": 42
  }
]
```

2. 运行自定义实验:
```bash
python src/experiment_controller.py custom --config_file my_experiments.json
```

### 单独使用各个组件

#### 设备管理器
```python
from src.device_manager import DeviceManager

dm = DeviceManager(verbose=True)
optimal_device = dm.get_optimal_device(memory_requirement_gb=2.0)
devices = dm.allocate_devices(num_processes=4)
```

#### 实验管理器
```python
from src.experiment_manager import ExperimentManager, ExperimentConfig

em = ExperimentManager()
experiments = em.create_baseline_comparison_experiments()
em.save_experiment_plan("my_plan.json")
```

#### 并行执行器
```python
from src.parallel_executor import ParallelExecutor

executor = ParallelExecutor(max_workers=4, verbose=True)
results = executor.execute_experiments(experiments)
```

#### 结果分析器
```python
from src.experiment_analyzer import ExperimentAnalyzer

analyzer = ExperimentAnalyzer("experiment_results")
analyzer.generate_visualizations()
report = analyzer.generate_comprehensive_report()
```

## 📊 实验结果分析

### 自动生成的文件

每次实验运行后，会在输出目录生成以下文件：

```
experiment_results/
├── system_info.json              # 系统配置信息
├── execution_report.json         # 执行报告
├── final_comprehensive_report.json # 综合分析报告
├── final_analysis/               # 可视化图表
│   ├── aggregator_comparison.png
│   ├── pol_effect_analysis.png
│   ├── attack_robustness_analysis.png
│   └── dataset_generalization.png
├── exp_<experiment_id>/          # 单个实验结果
│   ├── config.json              # 实验配置
│   ├── stdout.log               # 标准输出
│   ├── stderr.log               # 错误输出
│   ├── experiment_metadata.json # 实验元数据
│   └── detailed_results.json    # 详细结果
└── experiment_controller.log     # 控制器日志
```

### 远程分析

您可以将实验结果文件发送给我进行分析，我可以通过这些文件了解：
- 实验执行状态和成功率
- 性能指标和对比结果
- 错误信息和问题诊断
- 改进建议和优化方向

## 🎛️ 参数配置

### 核心参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `model_name` | 模型类型 | LeNet | LeNet, ResNet, TextCNN |
| `dataset` | 数据集 | MNIST | MNIST, CIFAR, TREC, AGNEWS |
| `aggregator` | 聚合算法 | FedAvg | FedAvg, martFL, Krum, FLTrust |
| `n_participant` | 参与者数量 | 10 | 5, 10, 20, 50, 100 |
| `n_adversary` | 恶意参与者数量 | 0 | 0, 2, 5 |
| `attack` | 攻击类型 | None | label_flipping, backdoor, sybil |
| `global_epoch` | 全局训练轮数 | 20 | 10, 20, 30, 50 |
| `enable_pol` | 启用PoL | false | true, false |

### PoL专用参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `pol_verification_threshold` | 验证阈值 | 10.0 |
| `pol_verification_budget` | 验证预算 | 2 |
| `pol_entropy_analysis` | 熵分析 | false |
| `pol_quality_aware` | 质量感知 | false |
| `pol_storage_precision` | 存储精度 | float32 |

### 执行控制参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `max_workers` | 最大并行数 | 自动检测 |
| `output_dir` | 输出目录 | experiment_results |
| `timeout_seconds` | 超时时间 | 3600 |
| `verbose` | 详细输出 | true |

## 🔍 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用CPU或减少批次大小
   export CUDA_VISIBLE_DEVICES=""
   # 或在配置中设置 batch_size=16
   ```

2. **fork()错误（Windows/CUDA冲突）**
   ```bash
   export DISABLE_FORK=true
   export DISABLE_WATCHER=true
   ```

3. **并行执行失败**
   ```bash
   # 减少并行数
   python src/experiment_controller.py baseline --max_workers 1
   ```

4. **依赖缺失**
   ```bash
   pip install -r requirements.txt
   ```

### 调试模式

```bash
# 启用详细日志
python src/experiment_controller.py baseline --verbose

# 单个实验调试
cd src
python main.py --enable_pol --dataset MNIST --n_participant 3 --global_epoch 2
```

## 📈 性能优化建议

### 针对您的服务器配置

根据您的服务器配置（i7-13700 + 64GB内存 + 双GPU），建议：

1. **并行配置**:
   ```bash
   # 最优并行数：2-4个进程
   python src/experiment_controller.py comprehensive --max_workers 4
   ```

2. **内存优化**:
   ```python
   # 在配置中使用
   "pol_storage_precision": "float16"  # 节省50%存储
   "batch_size": 64  # 充分利用内存
   ```

3. **GPU利用**:
   ```bash
   # 确保GPU可用
   python run_experiments.py check
   ```

## 📝 实验建议

### 论文级实验设计

1. **基线对比** (1-2小时):
   ```bash
   python src/experiment_controller.py baseline
   ```

2. **攻击鲁棒性** (2-4小时):
   ```bash
   python src/experiment_controller.py attack
   ```

3. **扩展性分析** (1-3小时):
   ```bash
   python src/experiment_controller.py scalability
   ```

4. **综合评估** (6-12小时):
   ```bash
   python src/experiment_controller.py comprehensive
   ```

### 实验顺序建议

1. 先运行 `pol-demo` 验证基本功能
2. 再运行 `attack-demo` 验证防御效果
3. 最后运行 `benchmark` 获得完整结果

## 🤝 技术支持

如果遇到问题：
1. 查看日志文件 `experiment_controller.log`
2. 检查错误输出 `stderr.log`
3. 运行环境检查 `python run_experiments.py check`
4. 将相关文件发送给我进行分析

---

**祝您实验顺利！🎉**

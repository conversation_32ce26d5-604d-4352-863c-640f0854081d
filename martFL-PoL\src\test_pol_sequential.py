#!/usr/bin/env python3
"""
PoL顺序测试脚本 - 避免并行执行问题
"""

import os
import sys
import torch
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_pol_sequential():
    """
    顺序测试PoL功能，避免并行执行问题
    """
    print("🔍 开始PoL顺序测试...")
    
    # 设置环境
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 只使用一个GPU
    
    # 导入必要模块
    from config import Config
    from data_utils import get_data
    from train import BenignTrainingWithPoL
    from aggregator import Aggregator
    from pol_integration import ParticipantManager

    # 获取配置 - 模拟命令行参数
    import sys
    sys.argv = ['test_pol_sequential.py', '--enable_pol', '--n_participant', '2',
                '--global_epoch', '1', '--local_epoch', '1', '--root_dataset', '200']

    args = Config()

    # 确保PoL配置正确
    args.enable_pol = True
    args.n_participant = 2
    args.global_epoch = 1
    args.local_epoch = 1
    args.root_dataset = 200
    args.pol_verification_threshold = 10.0

    # 设置必要的属性
    args.exp_name = "MNIST-LeNet-2-None-0-uni-uni-FedAvg-False-test"
    args.client_model_name = "LeNet"
    args.device = "auto"
    args.optimizer = "SGD"
    args.learning_rate = 0.01
    
    print(f"📋 测试配置:")
    print(f"   参与者数量: {args.n_participant}")
    print(f"   全局轮数: {args.global_epoch}")
    print(f"   本地轮数: {args.local_epoch}")
    print(f"   数据集大小: {args.root_dataset}")
    print(f"   验证阈值: {args.pol_verification_threshold}")
    
    # 准备数据
    print("\n📊 准备数据...")
    train_loaders, test_loader = get_data(args)
    
    # 创建参与者管理器
    print("\n🔧 创建参与者管理器...")
    participants = ParticipantManager(
        n_participants=args.n_participant,
        proof_base_dir="proof_test",
        verification_threshold=args.pol_verification_threshold
    )
    
    # 创建聚合器
    aggregator = Aggregator(args, participants)
    
    # 顺序训练每个参与者
    print("\n🚀 开始顺序训练...")
    participant_models = {}
    
    for participant_id in range(args.n_participant):
        print(f"\n--- 训练参与者 {participant_id} ---")
        
        try:
            # 单独训练每个参与者
            dataloader = train_loaders[participant_id]
            
            print(f"   数据量: {len(dataloader.dataset)}")
            print(f"   批次数: {len(dataloader)}")
            
            # 使用PoL训练
            model = BenignTrainingWithPoL(
                exp_name=args.exp_name,
                client_model_name=args.client_model_name,
                dataloader=dataloader,
                loss_fn=torch.nn.CrossEntropyLoss(),
                device=args.device,
                local_epoch=args.local_epoch,
                optimizer_name=args.optimizer,
                learning_rate=args.learning_rate,
                participants=participants,
                participant_id=participant_id,
                global_epoch=0
            )
            
            participant_models[participant_id] = model
            print(f"   ✅ 参与者 {participant_id} 训练完成")
            
        except Exception as e:
            print(f"   ❌ 参与者 {participant_id} 训练失败: {e}")
            import traceback
            print(f"   详细错误: {traceback.format_exc()}")
            return False
    
    # 验证PoL证明
    print("\n🔍 验证PoL证明...")
    try:
        proof_dirs = []
        for participant_id in range(args.n_participant):
            proof_dir = f"proof_test/participant_{participant_id}_epoch_0"
            if os.path.exists(proof_dir):
                proof_dirs.append(proof_dir)
                print(f"   找到证明: {proof_dir}")
            else:
                print(f"   ⚠️ 证明不存在: {proof_dir}")
        
        if proof_dirs:
            verification_results = aggregator.verify_participant_proofs(proof_dirs)
            
            valid_count = sum(1 for r in verification_results if r['valid'])
            total_count = len(verification_results)
            
            print(f"\n📊 验证结果:")
            print(f"   总证明数: {total_count}")
            print(f"   有效证明: {valid_count}")
            print(f"   有效率: {valid_count/total_count*100:.1f}%")
            
            return valid_count > 0
        else:
            print("   ❌ 没有找到任何证明")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        import traceback
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🧪 PoL顺序测试工具")
    print("=" * 50)
    
    success = test_pol_sequential()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试成功！PoL功能正常工作")
    else:
        print("❌ 测试失败！需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

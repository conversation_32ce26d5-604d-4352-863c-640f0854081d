#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
顶会标准实验配置 - 符合ICML/NeurIPS/ICLR标准的完整实验套件
包含所有新增的聚合器、攻击类型和数据集
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.experiment_controller import ExperimentController
from src.experiment_manager import ExperimentConfig
import argparse

def create_comprehensive_top_tier_experiments():
    """创建符合顶会标准的综合实验"""
    
    print("🎯 创建顶会标准实验配置...")
    
    # 基线对比实验 - 扩展版
    baseline_experiments = []
    
    # 核心数据集组合
    core_datasets = ['MNIST', 'CIFAR', 'TREC']
    extended_datasets = ['AGNEWS', 'ImageNet100', 'TinyImageNet', 'IMDB']
    
    # 完整聚合器列表
    all_aggregators = ['FedAvg', 'martFL', 'Krum', 'FLTrust', 'FLAME', 'FedNova', 'SCAFFOLD', 'TrimmedMean', 'FedOpt']
    
    # 参与者规模
    participant_scales = [10, 20, 50, 100]
    
    print("📊 生成基线对比实验...")
    
    # 1. 核心基线对比 - 所有聚合器在核心数据集上
    for dataset in core_datasets:
        for aggregator in all_aggregators:
            for n_participants in [10, 20, 50]:
                for run in range(1, 6):  # 5次重复
                    # 无PoL基线
                    baseline_experiments.append(ExperimentConfig(
                        dataset=dataset,
                        aggregator=aggregator,
                        n_participant=n_participants,
                        global_epoch=30,
                        local_epoch=2,
                        enable_pol=False,
                        run_id=run,
                        seed=42 + run
                    ))
                    
                    # martFL + PoL实验
                    if aggregator == 'martFL':
                        baseline_experiments.append(ExperimentConfig(
                            dataset=dataset,
                            aggregator=aggregator,
                            n_participant=n_participants,
                            global_epoch=30,
                            local_epoch=2,
                            enable_pol=True,
                            pol_entropy_analysis=True,
                            pol_quality_aware=True,
                            run_id=run,
                            seed=42 + run
                        ))
    
    # 2. 扩展数据集验证
    for dataset in extended_datasets:
        for aggregator in ['FedAvg', 'martFL', 'FLAME']:  # 选择代表性聚合器
            for run in range(1, 4):  # 3次重复
                baseline_experiments.append(ExperimentConfig(
                    dataset=dataset,
                    aggregator=aggregator,
                    n_participant=20,
                    global_epoch=25,
                    enable_pol=(aggregator == 'martFL'),
                    run_id=run,
                    seed=42 + run
                ))
    
    print(f"   生成了 {len(baseline_experiments)} 个基线对比实验")
    
    # 攻击鲁棒性实验 - 扩展版
    attack_experiments = []
    
    # 完整攻击列表
    all_attacks = ['label_flipping', 'backdoor', 'sybil', 'rescaling', 'adaptive', 'model_replacement']
    
    # 鲁棒聚合器
    robust_aggregators = ['martFL', 'Krum', 'FLTrust', 'FLAME', 'TrimmedMean']
    
    print("⚔️ 生成攻击鲁棒性实验...")
    
    for dataset in ['MNIST', 'CIFAR', 'ImageNet100']:
        for attack in all_attacks:
            for aggregator in robust_aggregators:
                for n_participants in [20, 50]:
                    for adv_ratio in [0.2, 0.3, 0.4]:
                        n_adversary = max(1, int(n_participants * adv_ratio))
                        for run in range(1, 4):  # 3次重复
                            # 基线攻击实验
                            attack_experiments.append(ExperimentConfig(
                                dataset=dataset,
                                aggregator=aggregator,
                                attack=attack,
                                n_participant=n_participants,
                                n_adversary=n_adversary,
                                global_epoch=25,
                                enable_pol=False,
                                run_id=run,
                                seed=42 + run
                            ))
                            
                            # PoL防御实验（仅martFL）
                            if aggregator == 'martFL':
                                attack_experiments.append(ExperimentConfig(
                                    dataset=dataset,
                                    aggregator=aggregator,
                                    attack=attack,
                                    n_participant=n_participants,
                                    n_adversary=n_adversary,
                                    global_epoch=25,
                                    enable_pol=True,
                                    pol_entropy_analysis=True,
                                    pol_quality_aware=True,
                                    run_id=run,
                                    seed=42 + run
                                ))
    
    print(f"   生成了 {len(attack_experiments)} 个攻击鲁棒性实验")
    
    # 扩展性实验 - 大规模
    scalability_experiments = []
    
    print("📈 生成扩展性实验...")
    
    for dataset in ['MNIST', 'CIFAR', 'TREC']:
        for aggregator in ['FedAvg', 'martFL', 'FLAME', 'FedNova']:
            for n_participants in [50, 100, 200]:  # 大规模参与者
                for run in range(1, 3):  # 2次重复（大规模实验耗时较长）
                    scalability_experiments.append(ExperimentConfig(
                        dataset=dataset,
                        aggregator=aggregator,
                        n_participant=n_participants,
                        global_epoch=20,  # 减少轮数以节省时间
                        enable_pol=(aggregator == 'martFL'),
                        run_id=run,
                        seed=42 + run
                    ))
    
    print(f"   生成了 {len(scalability_experiments)} 个扩展性实验")
    
    # 非IID设置实验
    non_iid_experiments = []
    
    print("🔀 生成非IID设置实验...")
    
    # 不同的非IID程度
    non_iid_settings = [
        {'dirichlet_alpha': 0.1, 'description': 'highly_non_iid'},
        {'dirichlet_alpha': 0.5, 'description': 'moderately_non_iid'},
        {'dirichlet_alpha': 1.0, 'description': 'mildly_non_iid'},
        {'dirichlet_alpha': 10.0, 'description': 'nearly_iid'}
    ]
    
    for dataset in ['MNIST', 'CIFAR']:
        for setting in non_iid_settings:
            for aggregator in ['FedAvg', 'martFL', 'FLAME']:
                for run in range(1, 4):
                    non_iid_experiments.append(ExperimentConfig(
                        dataset=dataset,
                        aggregator=aggregator,
                        n_participant=20,
                        global_epoch=30,
                        enable_pol=(aggregator == 'martFL'),
                        # 这里可以添加非IID相关参数
                        run_id=run,
                        seed=42 + run
                    ))
    
    print(f"   生成了 {len(non_iid_experiments)} 个非IID实验")
    
    # 汇总所有实验
    all_experiments = {
        'baseline_comparison': baseline_experiments,
        'attack_robustness': attack_experiments,
        'scalability': scalability_experiments,
        'non_iid_evaluation': non_iid_experiments
    }
    
    total_experiments = sum(len(experiments) for experiments in all_experiments.values())
    
    print(f"\n🎯 顶会标准实验配置完成:")
    print(f"   📊 基线对比: {len(baseline_experiments)} 个实验")
    print(f"   ⚔️ 攻击鲁棒性: {len(attack_experiments)} 个实验")
    print(f"   📈 扩展性: {len(scalability_experiments)} 个实验")
    print(f"   🔀 非IID评估: {len(non_iid_experiments)} 个实验")
    print(f"   🎯 总计: {total_experiments} 个实验")
    print(f"\n预计运行时间: {total_experiments * 3 / 60:.1f} 小时 (假设每个实验3分钟)")
    
    return all_experiments

def run_top_tier_experiments(experiment_type: str = 'all', max_workers: int = None):
    """运行顶会标准实验"""
    
    print("🚀 启动顶会标准实验")
    print("=" * 60)
    
    # 创建实验配置
    all_experiments = create_comprehensive_top_tier_experiments()
    
    # 创建实验控制器
    controller = ExperimentController(
        output_dir="top_tier_experiment_results",
        max_workers=max_workers,
        verbose=True
    )
    
    if experiment_type == 'all':
        # 运行所有实验
        print("🎯 运行完整的顶会标准实验套件...")
        
        all_results = []
        for category, experiments in all_experiments.items():
            print(f"\n📋 执行 {category} 实验 ({len(experiments)} 个)")
            results = controller.run_custom_experiments(experiments)
            all_results.extend(results)
            
            # 生成中间分析
            controller._generate_intermediate_analysis(category)
        
        # 生成最终分析
        controller.generate_final_analysis()
        
        print(f"\n🎉 顶会标准实验完成！")
        print(f"   总实验数: {len(all_results)}")
        print(f"   成功: {len(controller.parallel_executor.completed_tasks)}")
        print(f"   失败: {len(controller.parallel_executor.failed_tasks)}")
        print(f"   结果保存在: top_tier_experiment_results/")
        
    else:
        # 运行特定类型的实验
        if experiment_type in all_experiments:
            experiments = all_experiments[experiment_type]
            print(f"🎯 运行 {experiment_type} 实验 ({len(experiments)} 个)")
            
            results = controller.run_custom_experiments(experiments)
            controller.generate_final_analysis()
            
            print(f"\n🎉 {experiment_type} 实验完成！")
        else:
            print(f"❌ 未知的实验类型: {experiment_type}")
            print(f"可用类型: {list(all_experiments.keys())}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='顶会标准实验执行器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
实验类型:
  all                - 运行所有顶会标准实验
  baseline_comparison - 基线对比实验
  attack_robustness  - 攻击鲁棒性实验
  scalability        - 扩展性实验
  non_iid_evaluation - 非IID评估实验

示例用法:
  python top_tier_experiments.py all --max_workers 4
  python top_tier_experiments.py baseline_comparison --max_workers 2
        """
    )
    
    parser.add_argument('experiment_type', 
                       choices=['all', 'baseline_comparison', 'attack_robustness', 'scalability', 'non_iid_evaluation'],
                       default='all',
                       nargs='?',
                       help='实验类型 (默认: all)')
    
    parser.add_argument('--max_workers', type=int, default=None,
                       help='最大并行工作进程数 (默认: 自动检测)')
    
    parser.add_argument('--preview', action='store_true',
                       help='仅预览实验配置，不执行')
    
    args = parser.parse_args()
    
    if args.preview:
        # 仅预览配置
        print("👀 预览顶会标准实验配置...")
        all_experiments = create_comprehensive_top_tier_experiments()
        print("\n预览完成！使用 --preview 参数可以查看实验配置而不执行。")
    else:
        # 执行实验
        run_top_tier_experiments(args.experiment_type, args.max_workers)

if __name__ == "__main__":
    main()

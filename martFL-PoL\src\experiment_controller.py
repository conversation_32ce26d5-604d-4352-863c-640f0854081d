#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验控制器 - 统一的实验执行入口
整合设备管理、实验配置、并行执行和结果分析
"""

import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional, Dict, Any
import logging
import json

# 导入自定义模块
from device_manager import get_device_manager
from experiment_manager import ExperimentManager, ExperimentConfig
from parallel_executor import ParallelExecutor
from experiment_analyzer import ExperimentAnalyzer

class ExperimentController:
    """实验控制器 - 统一管理整个实验流程"""
    
    def __init__(self, 
                 output_dir: str = "experiment_results",
                 max_workers: Optional[int] = None,
                 verbose: bool = True):
        """
        初始化实验控制器
        
        Args:
            output_dir: 实验输出目录
            max_workers: 最大并行工作进程数
            verbose: 是否输出详细信息
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.verbose = verbose
        
        # 初始化各个组件
        self.device_manager = get_device_manager(verbose=verbose)
        self.experiment_manager = ExperimentManager(str(self.output_dir))
        self.parallel_executor = ParallelExecutor(
            max_workers=max_workers,
            output_dir=str(self.output_dir),
            verbose=verbose
        )
        self.analyzer = ExperimentAnalyzer(str(self.output_dir))
        
        self.logger = self._setup_logger()
        
        # 保存系统信息
        self._save_system_info()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ExperimentController')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            log_file = self.output_dir / "experiment_controller.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _save_system_info(self):
        """保存系统信息"""
        system_info_file = self.output_dir / "system_info.json"
        self.device_manager.save_device_info(str(system_info_file))
    
    def run_baseline_experiments(self) -> List[Dict[str, Any]]:
        """运行基线对比实验"""
        self.logger.info("🚀 开始基线对比实验")
        
        # 生成实验配置
        experiments = self.experiment_manager.create_baseline_comparison_experiments()
        
        # 保存实验计划
        plan_file = self.output_dir / "baseline_experiment_plan.json"
        plan = {'baseline_comparison': experiments}
        self.experiment_manager.experiment_plan = plan
        self.experiment_manager.save_experiment_plan(str(plan_file))
        
        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiments,
            progress_callback=self._progress_callback
        )
        
        self.logger.info("✅ 基线对比实验完成")
        return results
    
    def run_attack_experiments(self) -> List[Dict[str, Any]]:
        """运行攻击鲁棒性实验"""
        self.logger.info("🛡️ 开始攻击鲁棒性实验")
        
        # 生成实验配置
        experiments = self.experiment_manager.create_attack_robustness_experiments()
        
        # 保存实验计划
        plan_file = self.output_dir / "attack_experiment_plan.json"
        plan = {'attack_robustness': experiments}
        self.experiment_manager.experiment_plan = plan
        self.experiment_manager.save_experiment_plan(str(plan_file))
        
        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiments,
            progress_callback=self._progress_callback
        )
        
        self.logger.info("✅ 攻击鲁棒性实验完成")
        return results
    
    def run_scalability_experiments(self) -> List[Dict[str, Any]]:
        """运行扩展性实验"""
        self.logger.info("📈 开始扩展性实验")
        
        # 生成实验配置
        experiments = self.experiment_manager.create_scalability_experiments()
        
        # 保存实验计划
        plan_file = self.output_dir / "scalability_experiment_plan.json"
        plan = {'scalability': experiments}
        self.experiment_manager.experiment_plan = plan
        self.experiment_manager.save_experiment_plan(str(plan_file))
        
        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiments,
            progress_callback=self._progress_callback
        )
        
        self.logger.info("✅ 扩展性实验完成")
        return results
    
    def run_comprehensive_experiments(self) -> List[Dict[str, Any]]:
        """运行综合实验"""
        self.logger.info("🎯 开始综合实验计划")
        
        # 生成综合实验计划
        plan = self.experiment_manager.create_comprehensive_experiment_plan()
        
        # 保存实验计划
        plan_file = self.output_dir / "comprehensive_experiment_plan.json"
        self.experiment_manager.save_experiment_plan(str(plan_file))
        
        # 按类别执行实验
        all_results = []
        
        for category, experiments in plan.items():
            self.logger.info(f"执行 {category} 实验 ({len(experiments)} 个)")
            
            results = self.parallel_executor.execute_experiments(
                experiments,
                progress_callback=self._progress_callback
            )
            all_results.extend(results)
            
            # 中间分析
            self._generate_intermediate_analysis(category)
        
        self.logger.info("✅ 综合实验计划完成")
        return all_results
    
    def run_custom_experiments(self, experiment_configs: List[ExperimentConfig]) -> List[Dict[str, Any]]:
        """运行自定义实验"""
        self.logger.info(f"🔧 开始自定义实验 ({len(experiment_configs)} 个)")
        
        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiment_configs,
            progress_callback=self._progress_callback
        )
        
        self.logger.info("✅ 自定义实验完成")
        return results
    
    def _progress_callback(self, completed: int, total: int, result: Any):
        """进度回调函数"""
        if self.verbose:
            progress = completed / total * 100
            status = "✅" if result.success else "❌"
            self.logger.info(f"{status} 进度: {completed}/{total} ({progress:.1f}%) - {result.experiment_id}")
    
    def _generate_intermediate_analysis(self, category: str):
        """生成中间分析结果"""
        try:
            # 重新加载分析器以获取最新数据
            analyzer = ExperimentAnalyzer(str(self.output_dir))
            
            # 生成分类报告
            category_dir = self.output_dir / f"analysis_{category}"
            category_dir.mkdir(exist_ok=True)
            
            analyzer.generate_visualizations(str(category_dir))
            
            report_file = category_dir / f"{category}_report.json"
            analyzer.generate_comprehensive_report(str(report_file))
            
            self.logger.info(f"中间分析结果已保存到: {category_dir}")
            
        except Exception as e:
            self.logger.warning(f"生成中间分析失败: {e}")
    
    def generate_final_analysis(self):
        """生成最终分析报告"""
        self.logger.info("📊 生成最终分析报告")
        
        try:
            # 重新加载分析器
            analyzer = ExperimentAnalyzer(str(self.output_dir))
            
            # 生成可视化
            viz_dir = self.output_dir / "final_analysis"
            analyzer.generate_visualizations(str(viz_dir))
            
            # 生成综合报告
            report_file = self.output_dir / "final_comprehensive_report.json"
            report = analyzer.generate_comprehensive_report(str(report_file))
            
            # 打印关键结果
            self._print_key_results(report)
            
            self.logger.info(f"最终分析报告已保存到: {report_file}")
            self.logger.info(f"可视化图表已保存到: {viz_dir}")
            
        except Exception as e:
            self.logger.error(f"生成最终分析失败: {e}")
    
    def _print_key_results(self, report: Dict[str, Any]):
        """打印关键结果摘要"""
        print("\n" + "="*80)
        print("🎯 实验结果摘要")
        print("="*80)
        
        basic_stats = report.get('basic_statistics', {})
        print(f"📊 总实验数: {basic_stats.get('total_experiments', 0)}")
        print(f"✅ 成功实验: {basic_stats.get('successful_experiments', 0)}")
        print(f"🔬 PoL实验: {basic_stats.get('pol_experiments', 0)}")
        print(f"⚔️ 攻击实验: {basic_stats.get('attack_experiments', 0)}")
        print(f"📚 数据集数: {basic_stats.get('unique_datasets', 0)}")
        print(f"🔄 聚合器数: {basic_stats.get('unique_aggregators', 0)}")
        
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("="*80 + "\n")

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='martFL-PoL 实验控制器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
实验类型说明:
  baseline    - 基线对比实验 (FedAvg vs martFL vs martFL+PoL)
  attack      - 攻击鲁棒性实验 (各种攻击场景)
  scalability - 扩展性实验 (不同参与者数量)
  comprehensive - 综合实验 (包含以上所有类型)
  custom      - 自定义实验 (从配置文件加载)

示例用法:
  python experiment_controller.py baseline --max_workers 4
  python experiment_controller.py comprehensive --output_dir my_results
  python experiment_controller.py custom --config_file my_experiments.json
        """
    )
    
    parser.add_argument('experiment_type', 
                       choices=['baseline', 'attack', 'scalability', 'comprehensive', 'custom'],
                       help='实验类型')
    
    parser.add_argument('--output_dir', type=str, default='experiment_results',
                       help='实验输出目录 (默认: experiment_results)')
    
    parser.add_argument('--max_workers', type=int, default=None,
                       help='最大并行工作进程数 (默认: 自动检测)')
    
    parser.add_argument('--config_file', type=str, default=None,
                       help='自定义实验配置文件 (仅用于custom类型)')
    
    parser.add_argument('--no_analysis', action='store_true',
                       help='跳过最终分析')
    
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='输出详细信息')
    
    parser.add_argument('--quiet', action='store_true',
                       help='静默模式')
    
    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 设置详细程度
    verbose = args.verbose and not args.quiet
    
    # 创建实验控制器
    controller = ExperimentController(
        output_dir=args.output_dir,
        max_workers=args.max_workers,
        verbose=verbose
    )
    
    try:
        # 根据实验类型执行
        if args.experiment_type == 'baseline':
            results = controller.run_baseline_experiments()
        elif args.experiment_type == 'attack':
            results = controller.run_attack_experiments()
        elif args.experiment_type == 'scalability':
            results = controller.run_scalability_experiments()
        elif args.experiment_type == 'comprehensive':
            results = controller.run_comprehensive_experiments()
        elif args.experiment_type == 'custom':
            if not args.config_file:
                print("错误: custom类型需要指定 --config_file")
                sys.exit(1)
            
            # 加载自定义配置
            with open(args.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            experiments = [ExperimentConfig(**config) for config in config_data]
            results = controller.run_custom_experiments(experiments)
        
        # 生成最终分析
        if not args.no_analysis:
            controller.generate_final_analysis()
        
        print(f"\n🎉 实验完成！结果保存在: {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 实验执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

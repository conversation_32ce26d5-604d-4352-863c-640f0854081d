#!/usr/bin/env python3
"""
PoL验证调试工具
用于诊断验证失败的具体原因
"""

import os
import sys
import json
from pol_integration import ProofVerifier

def debug_verification_failure(proof_dir, threshold=10.0):
    """
    调试单个证明的验证失败原因
    
    Args:
        proof_dir: 证明目录
        threshold: 验证阈值
    """
    print(f"\n🔍 调试证明: {proof_dir}")
    print(f"📊 使用阈值: {threshold}")
    
    if not os.path.exists(proof_dir):
        print(f"❌ 证明目录不存在: {proof_dir}")
        return
    
    # 检查文件完整性
    required_files = ["metadata.json", "weights_sequence.json"]
    for file in required_files:
        file_path = os.path.join(proof_dir, file)
        if os.path.exists(file_path):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 缺失")
            return
    
    # 加载元数据
    try:
        with open(os.path.join(proof_dir, "metadata.json"), 'r') as f:
            metadata = json.load(f)
        print(f"📋 元数据加载成功")
        print(f"   参与者ID: {metadata.get('participant_id', 'N/A')}")
        print(f"   训练步数: {metadata.get('total_steps', 'N/A')}")
        print(f"   检查点数: {metadata.get('checkpoint_count', 'N/A')}")
    except Exception as e:
        print(f"❌ 元数据加载失败: {e}")
        return
    
    # 加载权重序列
    try:
        with open(os.path.join(proof_dir, "weights_sequence.json"), 'r') as f:
            weights_sequence = json.load(f)
        print(f"📊 权重序列加载成功，长度: {len(weights_sequence)}")
    except Exception as e:
        print(f"❌ 权重序列加载失败: {e}")
        return
    
    # 创建验证器并验证
    verifier = ProofVerifier(verification_threshold=threshold)
    result = verifier.verify_proof(proof_dir, model_architecture=None)
    
    print(f"\n📋 验证结果:")
    print(f"   有效性: {result['valid']}")
    
    if not result['valid']:
        print(f"   失败原因: {result.get('error', '未知')}")
        
        # 如果有详细的验证信息，显示距离数据
        if 'verification_details' in result and 'trajectory' in result['verification_details']:
            trajectory = result['verification_details']['trajectory']
            if 'verification_details' in trajectory:
                details = trajectory['verification_details']
                print(f"\n📊 详细验证信息:")
                print(f"   总更新数: {len(details)}")
                
                for i, detail in enumerate(details):
                    distance = detail.get('distance', 'N/A')
                    valid = detail.get('valid', False)
                    threshold_used = detail.get('threshold', threshold)
                    print(f"   更新{i}: L2距离={distance:.6f}, 阈值={threshold_used}, 有效={valid}")
                    
                    if isinstance(distance, (int, float)) and distance > threshold_used:
                        print(f"      ⚠️ 距离超过阈值 {distance:.6f} > {threshold_used}")
    else:
        print(f"   质量分数: {result.get('quality_score', 'N/A')}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python debug_pol_verification.py <证明目录> [阈值]")
        print("示例: python debug_pol_verification.py proof/participant_1_epoch_0_20250721_114359 10.0")
        return
    
    proof_dir = sys.argv[1]
    threshold = float(sys.argv[2]) if len(sys.argv) > 2 else 10.0
    
    debug_verification_failure(proof_dir, threshold)

def debug_all_recent_proofs(threshold=10.0):
    """调试最近的所有证明"""
    proof_base_dir = "proof"
    
    if not os.path.exists(proof_base_dir):
        print(f"❌ 证明目录不存在: {proof_base_dir}")
        return
    
    # 找到最近的证明目录
    proof_dirs = []
    for item in os.listdir(proof_base_dir):
        item_path = os.path.join(proof_base_dir, item)
        if os.path.isdir(item_path) and item.startswith("participant_"):
            proof_dirs.append(item_path)
    
    # 按时间排序，取最近的
    proof_dirs.sort(reverse=True)
    recent_proofs = proof_dirs[:5]  # 最近的5个
    
    print(f"🔍 调试最近的 {len(recent_proofs)} 个证明:")
    for proof_dir in recent_proofs:
        debug_verification_failure(proof_dir, threshold)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，调试所有最近的证明
        debug_all_recent_proofs()
    else:
        main()

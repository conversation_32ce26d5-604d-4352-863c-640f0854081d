#!/usr/bin/env python3
"""
martFL + PoL 整合演示脚本
快速演示核心功能
"""

import os
import sys
import torch
import numpy as np
import tempfile
import shutil
from datetime import datetime

# 添加源码路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pol_utils import *
from pol_integration import ProofGenerator, ProofVerifier


def demo_proof_generation_and_verification():
    """演示学习证明生成和验证的完整流程"""
    print("🚀 martFL + PoL 整合演示")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"📁 临时目录: {temp_dir}")
    
    try:
        # 1. 创建证明验证器
        print("\n1️⃣ 创建证明验证器")
        verifier = ProofVerifier(verification_threshold=0.2)
        print("✅ 证明验证器创建成功")

        # 2. 模拟多个参与者的训练和证明生成
        print("\n2️⃣ 模拟参与者训练和证明生成")

        participants = [0, 1, 2]  # 3个参与者
        proof_dirs = []

        for participant_id in participants:
            print(f"\n👤 参与者 {participant_id} 开始训练...")

            # 创建证明生成器
            generator = ProofGenerator(participant_id=participant_id, proof_base_dir=temp_dir)
            
            # 开始证明生成
            proof_dir = generator.start_proof_generation(global_epoch=1, local_epoch=5)
            
            # 创建模型（每个参与者使用稍有不同的模型）
            model = torch.nn.Sequential(
                torch.nn.Linear(20, 10),
                torch.nn.ReLU(),
                torch.nn.Linear(10, 5),
                torch.nn.ReLU(),
                torch.nn.Linear(5, 2)
            )
            optimizer = torch.optim.SGD(model.parameters(), lr=0.01 + participant_id * 0.005)
            
            # 模拟训练过程
            for step in range(15):
                # 生成随机训练数据
                dummy_input = torch.randn(32, 20)
                dummy_target = torch.randint(0, 2, (32,))
                
                # 训练步骤
                optimizer.zero_grad()
                output = model(dummy_input)
                loss = torch.nn.CrossEntropyLoss()(output, dummy_target)
                loss.backward()
                optimizer.step()
                
                # 保存训练步骤到证明
                batch_indices = np.arange(step * 32, (step + 1) * 32)
                generator.save_training_step(model, optimizer, batch_indices)
            
            # 完成证明生成
            final_proof_dir = generator.finalize_proof(model, optimizer)
            proof_dirs.append(final_proof_dir)
            
            print(f"✅ 参与者 {participant_id} 训练完成，证明保存在: {os.path.basename(final_proof_dir)}")
        
        # 3. 验证所有学习证明
        print("\n3️⃣ 验证学习证明")
        
        verifier = ProofVerifier(verification_threshold=0.2)
        verification_results = verifier.verify_multiple_proofs(proof_dirs, participants)
        
        print("\n📊 验证结果:")
        for i, result in enumerate(verification_results):
            status = "✅ 有效" if result["valid"] else "❌ 无效"
            score = result.get('quality_score', 0.0)
            print(f"  参与者 {result['participant_id']}: {status} (分数: {score:.3f})")
            if not result["valid"]:
                error_msg = result.get('error', result.get('message', '未知错误'))
                print(f"    原因: {error_msg}")
        
        # 4. 计算聚合权重
        print("\n4️⃣ 计算聚合权重")
        
        weights = verifier.get_aggregation_weights(verification_results)
        print("📈 基于证明验证的聚合权重:")
        for i, weight in enumerate(weights):
            print(f"  参与者 {participants[i]}: {weight:.3f}")
        
        # 5. 生成验证报告
        print("\n5️⃣ 生成验证报告")
        
        report = verifier.generate_verification_report(verification_results)
        print("📋 验证摘要:")
        print(f"  总证明数: {report['summary']['total_proofs']}")
        print(f"  有效证明: {report['summary']['valid_proofs']}")
        print(f"  有效率: {report['summary']['validity_rate']:.1%}")
        print(f"  平均分数: {report['summary']['average_score']:.3f}")
        
        # 6. 展示核心价值
        print("\n6️⃣ 核心价值展示")
        print("🎯 martFL + PoL 整合的核心价值:")
        print("  ✨ 可信联邦学习: 通过学习证明验证参与者训练真实性")
        print("  ⚖️  质量保证: 基于证明质量调整聚合权重")
        print("  🛡️  恶意检测: 识别和过滤无效的模型更新")
        print("  📊 透明度: 提供详细的验证报告和质量分数")
        
        # 7. 模拟恶意参与者检测
        print("\n7️⃣ 恶意参与者检测演示")
        
        # 创建一个"恶意"参与者（不生成有效证明）
        malicious_dir = os.path.join(temp_dir, "malicious_participant")
        os.makedirs(malicious_dir, exist_ok=True)
        
        # 只创建空的元数据文件（缺少必要的证明文件）
        with open(os.path.join(malicious_dir, "metadata.json"), "w") as f:
            f.write('{"participant_id": 999, "type": "malicious"}')
        
        # 验证恶意参与者
        malicious_result = verifier.verify_proof(malicious_dir, model_architecture=None)
        malicious_result['participant_id'] = 999
        print(f"🚨 恶意参与者检测: {'检测到' if not malicious_result['valid'] else '未检测到'}")
        error_msg = malicious_result.get('error', malicious_result.get('message', '未知错误'))
        print(f"   原因: {error_msg}")
        
        print("\n🎉 演示完成！")
        print("\n💡 下一步:")
        print("   1. 运行完整的联邦学习: cd src && python main_pol.py")
        print("   2. 调整参数进行实验: python main_pol.py --help")
        print("   3. 查看详细文档: cat README.md")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n🧹 清理临时目录: {temp_dir}")


def show_project_structure():
    """展示项目结构"""
    print("\n📁 项目结构:")
    print("""
martFL-PoL/
├── src/
│   ├── pol_utils.py           # PoL工具函数
│   ├── pol_integration.py     # PoL集成核心模块  
│   ├── pol_participant.py     # 扩展参与者类
│   ├── pol_aggregator.py      # 扩展聚合器类
│   ├── main_pol.py           # 主程序
│   └── config_pol.py         # 配置文件
├── proof/                    # 学习证明存储目录
├── test_integration.py       # 集成测试
├── demo.py                   # 本演示脚本
└── README.md                 # 详细文档
""")


def main():
    """主函数"""
    show_project_structure()
    demo_proof_generation_and_verification()


if __name__ == "__main__":
    main()

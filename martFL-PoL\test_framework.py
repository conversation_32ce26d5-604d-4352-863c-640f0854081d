#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验框架测试脚本 - 验证所有组件是否正常工作
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path
import json
import time

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_device_manager():
    """测试设备管理器"""
    print("🔧 测试设备管理器...")
    
    try:
        from src.device_manager import DeviceManager, get_optimal_device
        
        # 测试设备检测
        dm = DeviceManager(verbose=False)
        
        # 基本功能测试
        assert hasattr(dm, 'system_info'), "系统信息检测失败"
        assert hasattr(dm, 'gpu_info'), "GPU信息检测失败"
        assert hasattr(dm, 'cpu_info'), "CPU信息检测失败"
        
        # 设备分配测试
        optimal_device = dm.get_optimal_device()
        assert optimal_device in ['cpu'] + [f'cuda:{i}' for i in range(10)], f"无效设备: {optimal_device}"
        
        devices = dm.allocate_devices(2)
        assert len(devices) == 2, "设备分配数量错误"
        
        # 快速接口测试
        device = get_optimal_device()
        assert device is not None, "快速接口失败"
        
        print("  ✅ 设备管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 设备管理器测试失败: {e}")
        return False

def test_experiment_manager():
    """测试实验管理器"""
    print("🧪 测试实验管理器...")
    
    try:
        from src.experiment_manager import ExperimentManager, ExperimentConfig
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            em = ExperimentManager(temp_dir)
            
            # 测试实验配置创建
            config = ExperimentConfig(
                model_name='LeNet',
                dataset='MNIST',
                aggregator='FedAvg',
                n_participant=5,
                enable_pol=True
            )
            
            assert config.model_name == 'LeNet', "配置创建失败"
            assert config.experiment_id != '', "实验ID生成失败"
            
            # 测试实验计划生成
            baseline_experiments = em.create_baseline_comparison_experiments()
            assert len(baseline_experiments) > 0, "基线实验生成失败"
            
            attack_experiments = em.create_attack_robustness_experiments()
            assert len(attack_experiments) > 0, "攻击实验生成失败"
            
            scalability_experiments = em.create_scalability_experiments()
            assert len(scalability_experiments) > 0, "扩展性实验生成失败"
            
            # 测试综合实验计划
            comprehensive_plan = em.create_comprehensive_experiment_plan()
            assert len(comprehensive_plan) == 3, "综合实验计划生成失败"
            
            # 测试保存和加载
            plan_file = Path(temp_dir) / "test_plan.json"
            em.save_experiment_plan(str(plan_file))
            assert plan_file.exists(), "实验计划保存失败"
            
            em2 = ExperimentManager(temp_dir)
            loaded_plan = em2.load_experiment_plan(str(plan_file))
            assert len(loaded_plan) > 0, "实验计划加载失败"
        
        print("  ✅ 实验管理器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 实验管理器测试失败: {e}")
        return False

def test_experiment_analyzer():
    """测试实验分析器"""
    print("📊 测试实验分析器...")

    try:
        from src.experiment_analyzer import ExperimentAnalyzer
        
        # 创建临时目录和模拟数据
        with tempfile.TemporaryDirectory() as temp_dir:
            results_dir = Path(temp_dir) / "experiment_results"
            results_dir.mkdir()
            
            # 创建模拟实验数据
            exp_dir = results_dir / "exp_test123"
            exp_dir.mkdir()
            
            # 模拟配置文件
            config = {
                "model_name": "LeNet",
                "dataset": "MNIST",
                "aggregator": "martFL",
                "enable_pol": True,
                "n_participant": 10
            }
            with open(exp_dir / "config.json", 'w') as f:
                json.dump(config, f)
            
            # 模拟输出日志
            stdout_content = """
Epoch: 1, Loss: 2.3026, Accuracy: 0.1127, Kappa: 0.0000, F1: 0.1000
Epoch: 2, Loss: 2.2935, Accuracy: 0.1135, Kappa: 0.0012, F1: 0.1020
Epoch: 3, Loss: 2.2844, Accuracy: 0.1143, Kappa: 0.0024, F1: 0.1040
PoL验证通过率: 95.5%
训练时间: 120.5s
            """
            with open(exp_dir / "stdout.log", 'w') as f:
                f.write(stdout_content)
            
            # 创建空的stderr文件
            with open(exp_dir / "stderr.log", 'w') as f:
                f.write("")
            
            # 测试分析器
            analyzer = ExperimentAnalyzer(str(results_dir))
            
            # 检查数据加载
            assert len(analyzer.experiment_data) > 0, "实验数据加载失败"
            assert analyzer.performance_data is not None, "性能数据未初始化"
            
            # 测试性能分析
            performance_analysis = analyzer.generate_performance_comparison()
            assert isinstance(performance_analysis, dict), "性能分析失败"
            
            # 测试报告生成
            report = analyzer.generate_comprehensive_report()
            assert 'basic_statistics' in report, "综合报告生成失败"
            assert 'recommendations' in report, "建议生成失败"
        
        print("  ✅ 实验分析器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 实验分析器测试失败: {e}")
        return False

def test_parallel_executor():
    """测试并行执行器（模拟模式）"""
    print("⚡ 测试并行执行器...")
    
    try:
        from src.parallel_executor import ParallelExecutor, TaskResult
        from src.experiment_manager import ExperimentConfig
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            executor = ParallelExecutor(
                max_workers=1,
                output_dir=temp_dir,
                timeout_seconds=60,
                verbose=False
            )
            
            # 检查初始化
            assert executor.max_workers >= 1, "并行数设置错误"
            assert executor.output_dir.exists(), "输出目录创建失败"
            
            # 测试设备队列
            assert not executor.device_queue.empty(), "设备队列为空"
            
            print("  ✅ 并行执行器测试通过（基础功能）")
            print("  ℹ️ 完整测试需要实际运行实验")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 并行执行器测试失败: {e}")
        return False

def test_experiment_controller():
    """测试实验控制器"""
    print("🎯 测试实验控制器...")

    try:
        # 使用简化分析器避免依赖问题
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent / 'src'))

        from device_manager import get_device_manager
        from experiment_manager import ExperimentManager
        from parallel_executor import ParallelExecutor
        from experiment_analyzer import ExperimentAnalyzer
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 手动创建组件以避免依赖问题
            device_manager = get_device_manager(verbose=False)
            experiment_manager = ExperimentManager(temp_dir)
            parallel_executor = ParallelExecutor(
                max_workers=1,
                output_dir=temp_dir,
                verbose=False
            )
            analyzer = ExperimentAnalyzer(temp_dir)

            # 检查组件初始化
            assert device_manager is not None, "设备管理器未初始化"
            assert experiment_manager is not None, "实验管理器未初始化"
            assert parallel_executor is not None, "并行执行器未初始化"
            assert analyzer is not None, "分析器未初始化"
        
        print("  ✅ 实验控制器测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 实验控制器测试失败: {e}")
        return False

def test_integration():
    """集成测试 - 运行一个最小实验"""
    print("🔗 运行集成测试...")
    
    try:
        # 检查原始main.py是否可以导入
        sys.path.insert(0, str(Path(__file__).parent / 'src'))
        
        # 测试配置导入
        from src.config import Config
        config = Config()
        assert hasattr(config, 'enable_pol'), "配置类缺少PoL选项"
        
        # 测试设备管理集成
        from src.device_manager import get_optimal_device
        device = get_optimal_device()
        assert device is not None, "设备获取失败"
        
        print("  ✅ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 martFL-PoL 实验框架测试")
    print("=" * 50)
    
    tests = [
        ("设备管理器", test_device_manager),
        ("实验管理器", test_experiment_manager),
        ("实验分析器", test_experiment_analyzer),
        ("并行执行器", test_parallel_executor),
        ("实验控制器", test_experiment_controller),
        ("集成测试", test_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！实验框架可以正常使用")
        return True
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试实验框架')
    parser.add_argument('--component', choices=['device', 'experiment', 'analyzer', 'executor', 'controller', 'integration'],
                       help='测试特定组件')
    
    args = parser.parse_args()
    
    if args.component:
        # 测试特定组件
        test_map = {
            'device': test_device_manager,
            'experiment': test_experiment_manager,
            'analyzer': test_experiment_analyzer,
            'executor': test_parallel_executor,
            'controller': test_experiment_controller,
            'integration': test_integration
        }
        
        if args.component in test_map:
            success = test_map[args.component]()
            sys.exit(0 if success else 1)
    else:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

import os
import time
import threading
from copy import deepcopy
import json

try:
    from smart_watcher import SmartWatcher as Watcher
except ImportError:
    from watcher import Watcher
from mythread import *
from config import *
from model import *
from participant import *
from dataset import *
from train import *
from train import BenignTrainingWithPoL  # 启用PoL训练函数
from aggregator import *
from model_saver import import_model,export_model,backup_model
from model_saver import backup_models as bk_models
from result_saver import *
import warnings
warnings.filterwarnings('ignore')
from early_stopping import EarlyStopping

# 导入新的实验框架组件
try:
    from device_manager import get_optimal_device
    DEVICE_MANAGER_AVAILABLE = True
except ImportError:
    DEVICE_MANAGER_AVAILABLE = False


def main():
    """
    martFL主程序 - 优雅集成PoL功能
    保持原有martFL流程不变，PoL作为可选增强功能
    """
    import torch
    import multiprocessing

    print("🚀 martFL 启动...")

    # 启动Watcher（原有功能）
    disable_watcher = os.environ.get('DISABLE_WATCHER', 'false').lower() == 'true'
    if not disable_watcher:
        try:
            Watcher()
            print("✅ Watcher启动成功")
        except Exception as e:
            print(f"⚠️ Watcher启动失败，继续运行: {e}")

    # 加载配置
    Args = Config()

    # PoL功能状态
    if Args.enable_pol:
        print(f"✅ martFL + PoL模式，设备: {Args.device}")
        print(f"   证明目录: {Args.pol_proof_dir}")
        print(f"   验证阈值: {Args.pol_verification_threshold}")
    else:
        print(f"✅ 标准martFL模式，设备: {Args.device}")

    Exp_Name = Args.dataset + '-' + Args.model_name + '-' + \
        str(Args.n_participant) + '-' + Args.attack + '-' + \
        str(Args.n_adversary) + '-' + Args.sample_split + '-' + \
        Args.class_split + '-' + Args.aggregator + '-' + str(Args.quantization)

    if 'martFL' in Args.aggregator:
        Exp_Name = Exp_Name + '-' + str(Args.change_base)

    if Args.class_split == 'rand':
        Exp_Name = Exp_Name + '-' + str(Args.alpha)

    Exp_Name = Exp_Name + '-' + str(int(time.time()))
    print('Exp_Name:' + Exp_Name)
    print('change_base:',str(Args.change_base))


    # 智能设备管理（增强版）
    if DEVICE_MANAGER_AVAILABLE and Args.device == 'auto':
        try:
            optimal_device = get_optimal_device(memory_requirement_gb=2.0)
            Args.device = optimal_device
            print(f"🎯 自动选择设备: {optimal_device}")
        except Exception as e:
            print(f"⚠️ 设备自动选择失败，使用默认设备: {e}")
            Args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    elif Args.device == 'auto':
        # 回退到原有逻辑
        Args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"🔄 使用默认设备选择: {Args.device}")

    # 实验记录增强
    experiment_metadata = {
        'experiment_id': os.environ.get('EXPERIMENT_ID', Exp_Name),
        'timestamp': time.time(),
        'config': vars(Args),
        'device_used': Args.device,
        'pol_enabled': Args.enable_pol
    }

    # 保存实验元数据
    output_dir = os.environ.get('EXPERIMENT_OUTPUT_DIR', 'save_result')
    os.makedirs(output_dir, exist_ok=True)

    metadata_file = os.path.join(output_dir, 'experiment_metadata.json')
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_metadata, f, indent=2, ensure_ascii=False)
    def get_optimal_device(device_arg):
        """智能设备检测和选择"""
        if device_arg == 'auto':
            # 自动检测最佳设备
            if torch.cuda.is_available():
                device = torch.device('cuda:0')
                print(f"✅ 自动检测到CUDA设备: {device}")
            else:
                device = torch.device('cpu')
                print(f"✅ 自动选择CPU设备: {device}")
        else:
            # 用户指定设备
            try:
                device = torch.device(device_arg)
                if device.type == 'cuda' and not torch.cuda.is_available():
                    print(f"⚠️ 指定的CUDA设备不可用，自动切换到CPU")
                    device = torch.device('cpu')
                else:
                    print(f"✅ 使用指定设备: {device}")
            except Exception as e:
                print(f"⚠️ 设备初始化失败: {e}，使用CPU")
                device = torch.device('cpu')
        return device

    device = get_optimal_device(Args.device)
    # 更新配置中的设备信息，以便正确显示
    Args.device = device
    attack = Args.attack
    if attack is not None:
        if Args.dataset == 'MNIST':
            before_label = [1,7]
            after_label = [7,1]
        elif Args.dataset == 'FMNIST' :
            before_label = [7,9]
            after_label = [9,7]
        elif Args.dataset == 'CIFAR':
            before_label = [1,9]
            after_label = [9,1]
        else:
            before_label = [0,1]
            after_label = [1,0]
    
    n_participants = Args.n_participant
    n_adversaries = Args.n_adversary
    learning_rate = Args.learning_rate

    # Prepare Data
    print('# Prepare Data.')

    participants = Participant(np=n_participants,
                               dataset=Args.dataset,
                               batch_size=Args.batch_size,
                               output_dim=dataset_output_dim(Args.dataset),
                               root_dataset=Args.root_dataset,
                               sample_split=Args.sample_split,
                               class_split=Args.class_split,bias=Args.alpha,
                               server_sample_ratio=Args.server_ratio,
                               device=Args.device,
                               enable_pol=Args.enable_pol,
                               proof_base_dir="proof",
                               pol_save_freq=10)

    train_dataloaders = participants.train_dataloaders
    test_dataloader = participants.test_dataloader

    print('Data Distribution:')
    for i,data_distr in enumerate(participants.data_distribution):
        print('Client:',i , data_distr, sum(data_distr))
    
    participants.clean_dataset()

    Model = get_model(Args.model_name)
    
    if Args.model_name == 'TextCNN':
        INPUT_DIM = participants.embedding['embed_num']
        LABEL_DIM = participants.embedding['class_num']
        PAD_IDX   = participants.embedding['pad_token']
        model_structure = Model(INPUT_DIM,LABEL_DIM,PAD_IDX)
    else:
        model_structure = Model()
    
    client_models = [get_model_name(cid) for cid in range(n_participants)]
    for cmodel in client_models:
        export_model(Exp_Name,cmodel,deepcopy(model_structure))

    #backup_models = [backup_model(Exp_Name,cmodel_name) for cmodel_name in client_models]
    backup_models = bk_models(Exp_Name,client_models)
    aggregator_name = Args.aggregator
    aggregator = Aggregator(Exp_Name,n_participants,n_adversaries,backup_models,client_models,model_structure,Args.quantization,device,
                           enable_pol=Args.enable_pol, verification_threshold=Args.pol_verification_threshold, strict_mode=False)
    

    filename = save_info(Exp_Name,Args.learning_rate,Args.optimizer,
                           Args.loss_fn,Args.batch_size,
                           Args.global_epoch,Args.local_epoch,participants.data_distribution,Args.__dict__)
    print('Exp_INFO:',filename)
    
    total_loss = []
    total_acc = []
    total_attack_acc = []
    total_kp = []
    total_f1 = []
    total_baselines = []
    total_baselines_scores = []
    
    early_stopping = EarlyStopping(patience=100, verbose=True, delta=0, 
        path=MODEL_PATH + Exp_Name +'/' + 'BestGlobalModel')
    
    for global_epoch in range(Args.global_epoch):
        
        backup_models = bk_models(Exp_Name,client_models)

        #Threads
        sem_num = Args.semaphore
        sem = threading.Semaphore(sem_num)
        threads = []
        
        # Federate Training
        print('# Federated Training. Epoch: {}. LR: {}.'.format(global_epoch+1,learning_rate))
        for cid in range(n_participants):
            
            # Normal User
            if cid < n_participants-n_adversaries:
                # 使用带PoL的训练函数
                if Args.enable_pol:
                    client_thread = MyThread(func=BenignTrainingWithPoL,args=(Exp_Name,client_models[cid], train_dataloaders[cid],
                    get_loss_function(Args.loss_fn), device, Args.local_epoch, Args.optimizer, learning_rate, cid, global_epoch, participants),semaphore=sem)
                else:
                    client_thread = MyThread(func=BenignTraining,args=(Exp_Name,client_models[cid], train_dataloaders[cid],
                    get_loss_function(Args.loss_fn), device, Args.local_epoch, Args.optimizer, learning_rate),semaphore=sem)
            # Malicious User
            else:
                print('## Malicious Client {} is Training. Attack = {}.'.format(cid,attack))
                if attack == 'sybil' and cid == n_participants-n_adversaries: 
                    client_model = import_model(Exp_Name,client_models[cid],device)
                    client_model = label_flip_train_model(client_model,train_dataloaders[cid],
                    get_loss_function(Args.loss_fn),device,Args.local_epoch, Args.optimizer,
                    learning_rate,before_label,after_label)
                    export_model(Exp_Name,client_models[cid],client_model)
                else:
                    client_thread = MyThread(func=MaliciousTraining,args=(Exp_Name,attack,client_models[cid], train_dataloaders[cid],
                    get_loss_function(Args.loss_fn), device, Args.local_epoch, Args.optimizer, learning_rate,before_label,after_label),semaphore=sem)
            
            if attack == 'sybil' and cid > n_participants-n_adversaries:
                pass
            else:
                client_thread.start()
                threads.append(client_thread)
        
        # 等待所有线程完成并收集结果 - 兼容原始martFL但增加容错性
        for t in threads:
            t.join()

        # 收集训练结果（martFL聚合需要）- 但对失败保持容错
        successful_results = []
        for t in threads:
            try:
                if hasattr(t, 'get_result'):
                    result = t.get_result()
                    if result is not None:
                        successful_results.append(result)
            except:
                # 忽略失败的线程，继续处理其他线程
                pass

        # 如果有成功的结果，继续聚合；如果全部失败，也继续（保持兼容性）
        if len(successful_results) > 0:
            print(f"✅ 收集到 {len(successful_results)} 个有效训练结果")
        else:
            print("⚠️ 未收集到训练结果，使用默认聚合策略")

        #Federated Aggregating
        print('# Federated Aggregating. Aggregator: {}'.format(aggregator_name))

        # ground_truth_baseline - 使用安全的设备加载
        try:
            previous_global_model = import_model(Exp_Name,backup_models[0],device)
            ground_truth_model = train_model(previous_global_model,test_dataloader,
                get_loss_function(Args.loss_fn),device,Args.local_epoch, Args.optimizer, learning_rate)
        except Exception as e:
            print(f"⚠️ 基准模型加载失败: {e}")
            if device.type == 'cuda':
                print("🔄 尝试使用CPU加载基准模型")
                cpu_device = torch.device('cpu')
                previous_global_model = import_model(Exp_Name,backup_models[0],cpu_device)
                ground_truth_model = train_model(previous_global_model,test_dataloader,
                    get_loss_function(Args.loss_fn),cpu_device,Args.local_epoch, Args.optimizer, learning_rate)
                # 更新设备为CPU
                device = cpu_device
            else:
                raise e

        # 收集学习证明（如果启用PoL）
        proof_dirs = []
        participant_ids = []
        if Args.enable_pol:
            print('# 收集学习证明')
            for cid in range(n_participants - n_adversaries):  # 只收集正常用户的证明
                proof_dir = participants.get_current_proof(cid)
                if proof_dir and os.path.exists(proof_dir):
                    proof_dirs.append(proof_dir)
                    participant_ids.append(cid)
                    print(f'客户端 {cid} 证明: {proof_dir}')
                else:
                    print(f'客户端 {cid} 证明缺失')

        # 使用带PoL的聚合或原始聚合
        if Args.enable_pol and proof_dirs:
            aggregate_weight, chosen_baseline, baseline_score = aggregator.martFL_with_PoL(
                global_epoch=global_epoch, server_dataloader=test_dataloader,
                loss_fn=get_loss_function(Args.loss_fn), change_base=Args.change_base,
                ground_truth_model=ground_truth_model, proof_dirs=proof_dirs, participant_ids=participant_ids)
        else:
            aggregate_weight, chosen_baseline, baseline_score = aggregator.martFL(global_epoch=global_epoch,
            server_dataloader=test_dataloader,loss_fn=get_loss_function(Args.loss_fn),change_base = Args.change_base,ground_truth_model=ground_truth_model)
        total_baselines.append(chosen_baseline)
        total_baselines_scores.append(baseline_score)
        
        
        print('# Federated Evaluating.')
        global_model = import_model(Exp_Name,client_models[0],device)
        if attack == 'label_flipping' or attack == 'sybil' or attack == 'backdoor':
            loss,acc,atk_acc,kp,f1 = label_flip_evaluate_model(global_model,test_dataloader, get_loss_function(Args.loss_fn), device,global_epoch,before_label,after_label,dataset_output_dim(Args.dataset),print_log=True)
        else:    
            loss,acc,kp,f1 = evaluate_model(global_model, test_dataloader, get_loss_function(Args.loss_fn), device, global_epoch,dataset_output_dim(Args.dataset),print_log=True)
            atk_acc = 0.0

        total_loss.append(loss)
        total_acc.append(acc)
        total_attack_acc.append(atk_acc)
        total_kp.append(kp)
        total_f1.append(f1)

        #Save Epoch Result
        print('# Save Result:',end=' ')
        filename = save_epoch_result(Exp_Name,loss,acc,atk_acc,aggregate_weight.tolist(),aggregate_weight.tolist() ,global_epoch+1,kp,f1)
        print(filename)
        
        #Early Stopping
        early_stopping(acc, global_model)
        if early_stopping.early_stop:
            break
    
    ft_result = None 
    if Args.fine_tuning:
        print("Fine_tuning",Args.fine_tuning)
        best_model = deepcopy(global_model)
        best_model.load_state_dict(torch.load(MODEL_PATH + Exp_Name +'/' + 'BestGlobalModel'))
        finetune_model = best_model.to(device)
        
        total_ft_loss = []
        total_ft_acc = []
        total_ft_attack_acc = []
        total_ft_kp = []
        total_ft_f1 = []
        
        for ft_epoch in range(Args.fine_tuning_epoch):
            finetune_model = fine_tuning_model(finetune_model, train_dataloaders[0],get_loss_function(Args.loss_fn) , device, 2, Args.optimizer, learning_rate)
            if attack == 'label_flipping' or attack == 'sybil' or attack == 'backdoor':
                ft_loss,ft_acc,ft_atk_acc,ft_kp,ft_f1 = label_flip_evaluate_model(finetune_model,test_dataloader, get_loss_function(Args.loss_fn), device,ft_epoch,before_label,after_label,dataset_output_dim(Args.dataset),print_log=True)
            else:   
                ft_loss,ft_acc,ft_kp,ft_f1 = evaluate_model(finetune_model, test_dataloader, get_loss_function(Args.loss_fn), device, ft_epoch,dataset_output_dim(Args.dataset),print_log=True)  
                ft_atk_acc = 0.0
            
            
            total_ft_loss.append(ft_loss)
            total_ft_acc.append(ft_acc)
            total_ft_attack_acc.append(ft_atk_acc)
            total_ft_kp.append(ft_kp)
            total_ft_f1.append(ft_f1)
            
        for i,model_name in enumerate(client_models):
            export_model(Exp_Name,model_name,finetune_model)
            
        ft_result = {
            'loss':total_ft_loss,
            'acc':total_ft_acc,
            'atk_acc':total_ft_attack_acc,
            'kp':total_ft_kp,
            'f1':total_ft_f1
        }
            
    #Save Result
    print('# Save Result:',end=' ')
    filename = save_result(Exp_Name,total_loss,total_acc,total_attack_acc,total_kp,total_f1,total_baselines,total_baselines_scores,ft_result)
    print(filename)

    # 增强实验结果记录
    try:
        final_results = {
            'experiment_id': experiment_metadata['experiment_id'],
            'final_accuracy': total_acc[-1] if total_acc else 0.0,
            'final_loss': total_loss[-1] if total_loss else 0.0,
            'final_attack_accuracy': total_attack_acc[-1] if total_attack_acc else 0.0,
            'final_kappa': total_kp[-1] if total_kp else 0.0,
            'final_f1': total_f1[-1] if total_f1 else 0.0,
            'accuracy_history': total_acc,
            'loss_history': total_loss,
            'attack_accuracy_history': total_attack_acc,
            'kappa_history': total_kp,
            'f1_history': total_f1,
            'training_time': time.time() - experiment_metadata['timestamp'],
            'converged_epoch': len(total_acc),
            'max_accuracy': max(total_acc) if total_acc else 0.0,
            'min_loss': min(total_loss) if total_loss else 0.0,
            'experiment_completed': True,
            'completion_timestamp': time.time()
        }

        # 保存详细结果
        results_file = os.path.join(output_dir, 'detailed_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)

        print(f"✅ 详细结果已保存到: {results_file}")

    except Exception as e:
        print(f"⚠️ 保存详细结果失败: {e}")

    clean_model(Exp_Name,n_participants)


if __name__ == '__main__':
    main()
